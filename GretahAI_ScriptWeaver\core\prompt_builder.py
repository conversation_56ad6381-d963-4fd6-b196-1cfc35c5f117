"""
Enhanced prompt builder for test script generation.

This module provides a modularized version of the generate_test_script_prompt function
with improved prompt quality, better error handling, and comprehensive logging.

Usage:
    from core.prompt_builder import generate_test_script_prompt

    prompt = generate_test_script_prompt(
        test_case, step_matches, test_data, website_url,
        step_table_entry, state
    )

Features:
1. Enhanced prompt quality with clear variable naming conventions
2. Robust locator generation and element handling guidelines
3. Comprehensive error handling with defensive programming
4. Detailed logging for debugging and troubleshooting
5. Backward compatibility with existing AI integration
"""

from __future__ import annotations

import json
import logging
import traceback
import sys
import inspect
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

# Helper utilities
def safe_int(val: Any, default: Optional[int] = None) -> Optional[int]:
    """Parse val to int or return default if that fails."""
    try:
        return int(val)
    except (TypeError, ValueError):
        return default


def get_by_pos(items: List[Any], pos: int, default: Any = None) -> Any:
    """Safely get item at position from list."""
    try:
        return items[pos] if 0 <= pos < len(items) else default
    except (IndexError, TypeError):
        return default


def pick_current_step(test_case: Dict[str, Any], step_table_entry: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Resolve the current test step we are about to generate code for."""
    steps: List[Dict[str, Any]] = test_case.get("Steps", [])
    if not steps:
        raise ValueError("Test case has no Steps defined.")

    if step_table_entry is not None:
        step_no = str(step_table_entry.get("step_no", ""))
        for step in steps:
            if str(step.get("Step No", "")) == step_no:
                return step

    # Fallback: first step
    logger.warning("Could not find matching step using step_table_entry, using first step as fallback")
    return steps[0]


def build_previous_steps_info(previous_step_numbers: List[str], test_case: Dict[str, Any]) -> str:
    """Build information about previously implemented steps."""
    if not previous_step_numbers:
        return ""

    info = "Previous steps that have been implemented:\n"
    steps = test_case.get('Steps', [])

    if not isinstance(steps, list):
        logger.warning(f"test_case.Steps is not a list: {type(steps)}")
        return info + "(Error: Steps data is invalid)\n"

    for step_no in previous_step_numbers:
        try:
            step_found = False
            for step in steps:
                if not isinstance(step, dict):
                    continue

                step_no_str = str(step_no) if step_no is not None else ""
                step_num_str = str(step.get('Step No', ''))

                if step_no_str == step_num_str:
                    info += f"\nStep {step_no}:\n"
                    info += f"Action: {step.get('Test Steps', 'N/A')}\n"
                    info += f"Expected: {step.get('Expected Result', 'N/A')}\n"
                    step_found = True
                    break

            if not step_found:
                info += f"\nStep {step_no}: (Step details not found in test case)\n"

        except Exception as e:
            logger.warning(f"Error processing step {step_no}: {type(e).__name__}: {str(e)}")
            info += f"\nStep {step_no}: (Error processing step details)\n"

    return info


def build_previous_context(state: Any) -> str:
    """Build context information from previous steps."""
    if not state:
        return ""

    context = "\nContext from previous steps:\n"

    # Browser initialization info
    try:
        if hasattr(state, 'browser_initialized') and state.browser_initialized:
            context += "- Browser has already been initialized\n"
    except Exception as e:
        logger.warning(f"Error checking browser_initialized: {e}")

    # Imports
    try:
        if hasattr(state, 'script_imports') and state.script_imports:
            if isinstance(state.script_imports, (list, tuple, set)):
                context += "- Imports from previous steps:\n"
                for imp in state.script_imports:
                    context += f"  {imp}\n"
    except Exception as e:
        logger.warning(f"Error processing script_imports: {e}")

    # Fixtures
    try:
        if hasattr(state, 'script_fixtures') and state.script_fixtures:
            if isinstance(state.script_fixtures, (list, tuple, set)):
                context += "- Fixtures from previous steps:\n"
                for fixture in state.script_fixtures:
                    try:
                        fixture_parts = fixture.split()
                        fixture_name = fixture_parts[1] if len(fixture_parts) > 1 else fixture
                        context += f"  {fixture_name}\n"
                    except Exception:
                        context += f"  {fixture}\n"
    except Exception as e:
        logger.warning(f"Error processing script_fixtures: {e}")

    # Functions
    try:
        if hasattr(state, 'script_functions') and state.script_functions:
            if isinstance(state.script_functions, dict):
                context += "- Helper functions from previous steps:\n"
                for func_name in state.script_functions.keys():
                    context += f"  {func_name}\n"
    except Exception as e:
        logger.warning(f"Error processing script_functions: {e}")

    # Variables
    try:
        if hasattr(state, 'script_variables') and state.script_variables:
            if isinstance(state.script_variables, dict):
                context += "- Shared variables from previous steps:\n"
                for var_name, var_value in state.script_variables.items():
                    context += f"  {var_name} = {var_value}\n"
    except Exception as e:
        logger.warning(f"Error processing script_variables: {e}")

    return context


def get_previous_step_numbers(current_step: Dict[str, Any], state: Any) -> List[str]:
    """Get list of previous step numbers that have been implemented."""
    if not (state and hasattr(state, 'previous_scripts') and state.previous_scripts):
        return []

    try:
        current_step_no = str(current_step.get('Step No', '0'))
        if not current_step_no or current_step_no.strip() == '':
            current_step_no = '0'

        logger.debug(f"Current step number: {current_step_no}")

        if not isinstance(state.previous_scripts, dict):
            logger.warning(f"state.previous_scripts is not a dictionary: {type(state.previous_scripts)}")
            return []

        previous_step_numbers = []
        for step_no in list(state.previous_scripts.keys()):
            try:
                step_no_int = safe_int(step_no)
                current_step_no_int = safe_int(current_step_no)

                logger.debug(f"Comparing step numbers: step_no={step_no}→{step_no_int}, current_step_no={current_step_no}→{current_step_no_int}")

                if step_no_int is not None and current_step_no_int is not None and step_no_int < current_step_no_int:
                    previous_step_numbers.append(step_no)
            except Exception as e:
                logger.warning(f"Error processing step number: {step_no}, error: {str(e)}")
                continue

        # Sort previous steps
        try:
            def safe_sort_key(x):
                val = safe_int(x, -1)
                return val if val is not None else -1

            previous_step_numbers.sort(key=safe_sort_key)
            logger.info(f"Found {len(previous_step_numbers)} previous steps with scripts: {', '.join(previous_step_numbers)}")
        except Exception as e:
            logger.warning(f"Error sorting previous step numbers: {str(e)}")

        return previous_step_numbers

    except Exception as e:
        logger.error(f"Error processing previous scripts: {e}")
        return []


# Main entry point
def generate_test_script_prompt(
    test_case: Dict[str, Any],
    step_matches: Dict[str, Any],
    test_data: Dict[str, Any],
    website_url: str,
    step_table_entry: Optional[Dict[str, Any]] = None,
    state: Optional[Any] = None,
) -> str:
    """
    Generate a comprehensive prompt for test script generation.

    This function maintains the exact same signature as the original in core/ai.py
    while providing enhanced prompt quality and better error handling.

    Args:
        test_case: The test case to generate a script for
        step_matches: Dictionary of element matches for test steps
        test_data: Dictionary of test data values
        website_url: The URL of the website to test
        step_table_entry: The step table entry for the step (optional)
        state: The application state manager instance for script continuity (optional)

    Returns:
        str: The enhanced prompt for test script generation

    Raises:
        TypeError: If test_case is not a dictionary
        ValueError: If test case has no Steps defined
    """
    try:
        # Validate inputs
        if not isinstance(test_case, dict):
            raise TypeError("test_case must be a dict")

        if not test_case or not test_case.get('Steps'):
            raise ValueError("Test case has no Steps defined. Cannot generate script.")

        # Add debug log after input validation
        logger.debug(
            "DEBUG prompt: len(Steps)=%s, step_no=%s, prev_keys=%s",
            len(test_case.get('Steps', [])),
            step_table_entry.get('step_no') if step_table_entry else 'N/A',
            list(state.previous_scripts.keys()) if (state and hasattr(state,'previous_scripts')) else []
        )

        current_step = pick_current_step(test_case, step_table_entry)
        previous_step_numbers = get_previous_step_numbers(current_step, state)
        previous_steps_info = build_previous_steps_info(previous_step_numbers, test_case)
        previous_context = build_previous_context(state)

        # Log that we're using the merge_step_scripts approach
        if previous_steps_info:
            logger.info("Using merge_step_scripts approach for script continuity instead of including full scripts in prompt")

        # Build the comprehensive prompt template
        prompt = f"""
        Generate a PyTest script for a single test step that maintains continuity with previous steps:

        ## Context

        Test Case: {test_case.get('Test Case ID')}
        Objective: {test_case.get('Test Case Objective')}

        Step {current_step.get('Step No')}:
        Action: {current_step.get('Test Steps')}
        Expected: {current_step.get('Expected Result')}

        Element Matches:
        {json.dumps(step_matches, indent=2)}

        Test Data:
        {json.dumps(test_data, indent=2)}

        {previous_steps_info}
        {previous_context}

        ## Instructions to the AI

        1. Produce **only** Python code (no markdown).
        2. Implement **exactly** this step.
        3. Create a PyTest function `test_step{step_no}_{slug}`.
        4. Use the `driver` fixture (module‑scope Chrome with custom UA).
        5. Locate elements via `WebDriverWait(10)` + suitable EC.
        6. If action is navigation (`navigate|open|visit`):
           – `driver.get("{url}")` then assert URL (`EC.url_to_be|contains`).
           – No other element checks unless **Expected** mentions "title".
        7. After each user action add `time.sleep(random.uniform(0.5,1.5))`.
        8. On failure capture PNG to `./screenshots/` (create dir if needed).
        9. Use test_data keys verbatim; descriptive snake_case for locals.
        10. Import/define only what you use – duplicates will be hoisted later.

        Return the code.
        """

        return prompt.strip()

    except Exception as e:
        # Enhanced error handling with detailed diagnostics
        logger.error(f"Error generating test script prompt: {e}")
        logger.error(f"Error type: {type(e).__name__}")

        # Get traceback information
        tb = sys.exc_info()[2]
        if tb:
            tb_frame = tb.tb_frame
            line_no = tb.tb_lineno

            # Get the source code of the line that caused the error
            try:
                source_lines, starting_line = inspect.getsourcelines(tb_frame)
                error_line = source_lines[line_no - starting_line - 1].strip()
            except Exception:
                error_line = "Could not retrieve source line"

            # Get the full traceback
            tb_str = traceback.format_exc()
            logger.error(f"Traceback:\n{tb_str}")

            # Handle IndexError specifically with enhanced diagnostics
            if isinstance(e, IndexError):
                # Extract variable information from the frame locals
                locals_dict = tb_frame.f_locals

                # Try to identify the list and index that caused the error
                list_vars = {name: value for name, value in locals_dict.items()
                            if isinstance(value, (list, tuple, dict)) and name != 'self'}

                # Build diagnostic information
                diagnostics = []
                for name, value in list_vars.items():
                    if isinstance(value, (list, tuple)):
                        diagnostics.append(f"{name}: type={type(value).__name__}, length={len(value)}")
                    elif isinstance(value, dict):
                        diagnostics.append(f"{name}: type=dict, keys={list(value.keys())}, length={len(value)}")

                # Create detailed error message
                error_msg = (
                    f"IndexError at line {line_no} in generate_test_script_prompt: {str(e)}\n"
                    f"Error occurred in line: {error_line}\n"
                    f"Variable state at time of error:\n"
                    f"{chr(10).join(diagnostics)}\n"
                    f"test_case has Steps: {bool(test_case and test_case.get('Steps'))}\n"
                    f"step_table_entry: {step_table_entry}\n"
                    f"state has previous_scripts: {bool(state and hasattr(state, 'previous_scripts'))}"
                )

                logger.error(error_msg)
                return error_msg
            else:
                # For other exceptions, return a user-friendly error message
                error_msg = (
                    f"Error generating test script prompt: {type(e).__name__} at line {line_no}: {str(e)}\n"
                    f"Error occurred in line: {error_line}"
                )
                logger.error(error_msg)
                return error_msg
        else:
            # Fallback if no traceback available
            error_msg = f"Error generating test script prompt: {type(e).__name__}: {str(e)}"
            logger.error(error_msg)
            return error_msg
