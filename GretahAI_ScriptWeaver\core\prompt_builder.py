"""
Refactored prompt builder – smaller helpers, leaner prompt.

Usage:
    from prompt_builder import generate_test_script_prompt

    prompt = generate_test_script_prompt(test_case, step_matches, test_data, website_url,
                                         step_table_entry, state)

The function now:
1. Picks the current step deterministically.
2. Summarises prior context into two compact lines (token‑budget friendly).
3. Fills a trimmed template (≈ 600 words static).
4. Returns the prompt text; errors raise exceptions (caller decides how to handle).
"""

from __future__ import annotations

import json
import logging
import textwrap
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)

# ──────────────────────────────────────────────────────────────────────────────
# Helper utilities
# ──────────────────────────────────────────────────────────────────────────────

def safe_int(val: Any, default: int | None = None) -> int | None:  # noqa: D401
    """Parse *val* to ``int`` or return *default* if that fails."""
    try:
        return int(val)  # type: ignore[arg-type]
    except (TypeError, ValueError):
        return default


def pick_current_step(test_case: Dict[str, Any], step_table_entry: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Resolve the *current* test‑step we are about to generate code for."""
    steps: List[Dict[str, Any]] = test_case.get("Steps", [])
    if not steps:
        raise ValueError("Test case has no Steps defined.")

    if step_table_entry is not None:
        step_no = str(step_table_entry.get("step_no"))
        for step in steps:
            if str(step.get("Step No")) == step_no:
                return step

    # Fallback: first step
    return steps[0]


def summarise_previous_context(current_step: Dict[str, Any], state: Any, *, max_prev: int = 5) -> str:
    """Return a one‑liner summary of previously‑implemented steps + shared artefacts."""
    if not (state and getattr(state, "previous_scripts", None)):
        return ""

    curr_no = safe_int(current_step.get("Step No"), 0) or 0
    prev_nums = [n for n in (safe_int(k) for k in state.previous_scripts.keys()) if n is not None and n < curr_no]
    prev_nums = sorted(prev_nums)[-max_prev:]

    if not prev_nums:
        return ""

    bits: List[str] = [f"Previous steps implemented: {', '.join(map(str, prev_nums))}"]

    if getattr(state, "browser_initialized", False):
        bits.append("Browser already initialised")
    if funcs := getattr(state, "script_functions", None):
        bits.append("Helper functions: " + ", ".join(funcs.keys()))

    return " – ".join(bits)

# ──────────────────────────────────────────────────────────────────────────────
# Main entry‑point
# ──────────────────────────────────────────────────────────────────────────────

def generate_test_script_prompt(
    test_case: Dict[str, Any],
    step_matches: Dict[str, Any],
    test_data: Dict[str, Any],
    website_url: str,
    step_table_entry: Optional[Dict[str, Any]] = None,
    state: Optional[Any] = None,
) -> str:
    """Return a trimmed, instruction‑focused prompt for the LLM."""
    if not isinstance(test_case, dict):
        raise TypeError("test_case must be a dict")

    current_step = pick_current_step(test_case, step_table_entry)
    context = summarise_previous_context(current_step, state)

    template = textwrap.dedent(
        """
        ## Context
        Test Case: {case_id}
        Objective: {objective}
        {context}

        ## Step
        - No: {step_no}
        - Action: {action}
        - Expected: {expected}

        ## Data & Elements
        Element matches:
        ```json
        {matches}
        ```
        Test data:
        ```json
        {data}
        ```

        ## Requirements ✅
        * Implement **only** this step in a PyTest test function named `test_step{step_no}_{slug}`.
        * Use the `driver` fixture (module‑scoped) that instantiates a Chrome WebDriver with a custom User‑Agent.
        * Always locate elements via `WebDriverWait` (10 s) + suitable EC.
        * For navigation actions (`navigate|open|visit`), only `driver.get("{url}")` and assert URL; no element checks.
        * On `AssertionError` or `Exception`, capture a PNG screenshot in `./screenshots/`.
        * Use descriptive snake_case names; reuse provided `test_data` keys exactly.
        * Do **not** add code for other steps.

        Return **only** valid Python code.
        """
    )

    slug = (current_step.get("Test Steps", "action").split() or ["action"])[0].lower()

    prompt = template.format(
        case_id=test_case.get("Test Case ID"),
        objective=test_case.get("Test Case Objective"),
        context=context,
        step_no=current_step.get("Step No"),
        action=current_step.get("Test Steps"),
        expected=current_step.get("Expected Result"),
        matches=json.dumps(step_matches, indent=2),
        data=json.dumps(test_data, indent=2),
        url=website_url,
        slug=slug,
    )

    return prompt.strip()
