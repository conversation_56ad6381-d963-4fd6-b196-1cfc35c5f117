#!/usr/bin/env python3
"""
Test script for the enhanced prompt builder module.

This script tests the modularized generate_test_script_prompt function
to ensure it maintains backward compatibility and provides enhanced functionality.
"""

import sys
import os
import json

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.prompt_builder import generate_test_script_prompt

def test_basic_functionality():
    """Test basic functionality of the prompt builder."""
    print("Testing basic functionality...")
    
    # Sample test case
    test_case = {
        "Test Case ID": "TC001",
        "Test Case Objective": "Test login functionality",
        "Steps": [
            {
                "Step No": "1",
                "Test Steps": "Navigate to login page",
                "Expected Result": "Login page is displayed"
            },
            {
                "Step No": "2", 
                "Test Steps": "Enter username and password",
                "Expected Result": "Credentials are entered"
            }
        ]
    }
    
    # Sample step matches
    step_matches = {
        "username_field": "#username",
        "password_field": "#password",
        "login_button": "#login-btn"
    }
    
    # Sample test data
    test_data = {
        "username": "testuser",
        "password": "testpass"
    }
    
    # Sample step table entry
    step_table_entry = {
        "step_no": "1"
    }
    
    website_url = "https://example.com"
    
    try:
        # Generate prompt
        prompt = generate_test_script_prompt(
            test_case=test_case,
            step_matches=step_matches,
            test_data=test_data,
            website_url=website_url,
            step_table_entry=step_table_entry,
            state=None
        )
        
        # Verify prompt is generated
        assert isinstance(prompt, str), "Prompt should be a string"
        assert len(prompt) > 0, "Prompt should not be empty"
        assert "TC001" in prompt, "Test case ID should be in prompt"
        assert "Navigate to login page" in prompt, "Test step should be in prompt"
        assert "https://example.com" in prompt, "Website URL should be in prompt"
        
        print("✓ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def test_error_handling():
    """Test error handling of the prompt builder."""
    print("Testing error handling...")
    
    try:
        # Test with invalid test case
        prompt = generate_test_script_prompt(
            test_case=None,
            step_matches={},
            test_data={},
            website_url="https://example.com"
        )
        
        # Should return an error message
        assert "Error" in prompt or "error" in prompt.lower(), "Should return error message for invalid input"
        
        print("✓ Error handling test passed")
        return True
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False


def test_enhanced_features():
    """Test enhanced features of the prompt builder."""
    print("Testing enhanced features...")
    
    # Mock state object
    class MockState:
        def __init__(self):
            self.previous_scripts = {"1": "script1"}
            self.browser_initialized = True
            self.script_imports = ["import pytest", "from selenium import webdriver"]
            self.script_fixtures = ["@pytest.fixture def driver()"]
            self.script_functions = {"helper_func": "def helper_func()"}
            self.script_variables = {"base_url": "https://example.com"}
    
    test_case = {
        "Test Case ID": "TC002",
        "Test Case Objective": "Test enhanced features",
        "Steps": [
            {
                "Step No": "1",
                "Test Steps": "First step",
                "Expected Result": "First result"
            },
            {
                "Step No": "2",
                "Test Steps": "Second step", 
                "Expected Result": "Second result"
            }
        ]
    }
    
    step_table_entry = {"step_no": "2"}
    state = MockState()
    
    try:
        prompt = generate_test_script_prompt(
            test_case=test_case,
            step_matches={},
            test_data={},
            website_url="https://example.com",
            step_table_entry=step_table_entry,
            state=state
        )
        
        # Verify enhanced features
        assert "Previous steps that have been implemented" in prompt, "Should include previous steps info"
        assert "Context from previous steps" in prompt, "Should include previous context"
        assert "Browser has already been initialized" in prompt, "Should include browser state"
        
        print("✓ Enhanced features test passed")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced features test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing Enhanced Prompt Builder")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_error_handling,
        test_enhanced_features
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced prompt builder is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
